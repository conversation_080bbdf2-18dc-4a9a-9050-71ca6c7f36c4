using UnityEngine;
using BTR.SceneTransition.Debug;

namespace BTR.SceneTransition.Debug
{
    /// <summary>
    /// Simple test script to verify the scene transition diagnostic system is working
    /// </summary>
    public class SceneTransitionDiagnosticTest : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private bool runTestOnStart = false;
        [SerializeField] private bool testBTRDebugCompatibility = true;
        
        private void Start()
        {
            if (runTestOnStart)
            {
                RunDiagnosticTest();
            }
        }
        
        [ContextMenu("Run Diagnostic Test")]
        public void RunDiagnosticTest()
        {
            Debug.Log("[SceneTransitionDiagnosticTest] Starting diagnostic system test...");
            
            // Test 1: Check if diagnostic components exist
            TestComponentExistence();
            
            // Test 2: Test BTR.Debug compatibility layer
            if (testBTRDebugCompatibility)
            {
                TestBTRDebugCompatibility();
            }
            
            // Test 3: Test diagnostic system integration
            TestDiagnosticIntegration();
            
            Debug.Log("[SceneTransitionDiagnosticTest] Diagnostic system test completed!");
        }
        
        private void TestComponentExistence()
        {
            Debug.Log("[SceneTransitionDiagnosticTest] Testing component existence...");
            
            // Check for diagnostic components
            bool diagnosticExists = SceneTransitionDiagnostic.Instance != null;
            bool trackerExists = TransitionStateTracker.Instance != null;
            bool recoveryExists = SceneTransitionRecovery.Instance != null;
            bool monitorUIExists = FindFirstObjectByType<SceneTransitionMonitorUI>() != null;
            
            Debug.Log($"SceneTransitionDiagnostic: {(diagnosticExists ? "✅ Found" : "❌ Missing")}");
            Debug.Log($"TransitionStateTracker: {(trackerExists ? "✅ Found" : "❌ Missing")}");
            Debug.Log($"SceneTransitionRecovery: {(recoveryExists ? "✅ Found" : "❌ Missing")}");
            Debug.Log($"SceneTransitionMonitorUI: {(monitorUIExists ? "✅ Found" : "❌ Missing")}");
            
            if (!diagnosticExists || !trackerExists || !recoveryExists)
            {
                Debug.LogWarning("[SceneTransitionDiagnosticTest] Some diagnostic components are missing. Run SceneTransitionDiagnosticSetup to create them.");
            }
        }
        
        private void TestBTRDebugCompatibility()
        {
            Debug.Log("[SceneTransitionDiagnosticTest] Testing BTR.Debug compatibility layer...");
            
            try
            {
                // Test the compatibility layer
                BTR.Debug.Log("BTR.Debug.Log compatibility test - this should work!");
                BTR.Debug.LogWarning("BTR.Debug.LogWarning compatibility test");
                BTR.Debug.LogError("BTR.Debug.LogError compatibility test (this is expected)");
                
                Debug.Log("✅ BTR.Debug compatibility layer working correctly");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ BTR.Debug compatibility layer failed: {ex.Message}");
            }
        }
        
        private void TestDiagnosticIntegration()
        {
            Debug.Log("[SceneTransitionDiagnosticTest] Testing diagnostic system integration...");
            
            // Test diagnostic system if available
            if (SceneTransitionDiagnostic.Instance != null)
            {
                var diagnostic = SceneTransitionDiagnostic.Instance;
                
                Debug.Log($"Current State: {diagnostic.CurrentState}");
                Debug.Log($"Current Scene: {diagnostic.CurrentSceneName}");
                Debug.Log($"Transition Duration: {diagnostic.TransitionDuration:F2}s");
                Debug.Log($"Is Stuck: {diagnostic.IsStuck}");
                
                Debug.Log("✅ Diagnostic system integration working");
            }
            else
            {
                Debug.LogWarning("❌ SceneTransitionDiagnostic not found - cannot test integration");
            }
            
            // Test state tracker if available
            if (TransitionStateTracker.Instance != null)
            {
                var tracker = TransitionStateTracker.Instance;
                tracker.Log("Test", "Diagnostic test log entry", TransitionStateTracker.LogLevel.Info);
                
                Debug.Log("✅ State tracker integration working");
            }
            else
            {
                Debug.LogWarning("❌ TransitionStateTracker not found - cannot test integration");
            }
        }
        
        [ContextMenu("Test Scene Transition")]
        public void TestSceneTransition()
        {
            Debug.Log("[SceneTransitionDiagnosticTest] Testing scene transition...");
            
            var sceneManager = FindFirstObjectByType<SceneManagerBTR>();
            if (sceneManager != null)
            {
                Debug.Log("Found SceneManagerBTR - triggering test transition");
                sceneManager.DebugChangeToNextScene();
            }
            else
            {
                Debug.LogWarning("SceneManagerBTR not found - cannot test scene transition");
            }
        }
        
        [ContextMenu("Force Diagnostic Setup")]
        public void ForceDiagnosticSetup()
        {
            Debug.Log("[SceneTransitionDiagnosticTest] Forcing diagnostic setup...");
            
            var setup = FindFirstObjectByType<SceneTransitionDiagnosticSetup>();
            if (setup != null)
            {
                setup.SetupDiagnosticSystem();
                Debug.Log("✅ Diagnostic setup completed");
            }
            else
            {
                Debug.LogWarning("SceneTransitionDiagnosticSetup not found - creating one...");
                
                GameObject setupGO = new GameObject("SceneTransitionDiagnosticSetup");
                var setupComponent = setupGO.AddComponent<SceneTransitionDiagnosticSetup>();
                setupComponent.SetupDiagnosticSystem();
                
                Debug.Log("✅ Created and ran diagnostic setup");
            }
        }
        
        private void OnGUI()
        {
            if (!Application.isEditor) return;
            
            GUILayout.BeginArea(new Rect(10, 120, 200, 150));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("Diagnostic Test");
            
            if (GUILayout.Button("Run Test"))
            {
                RunDiagnosticTest();
            }
            
            if (GUILayout.Button("Test Transition"))
            {
                TestSceneTransition();
            }
            
            if (GUILayout.Button("Force Setup"))
            {
                ForceDiagnosticSetup();
            }
            
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
    }
}
