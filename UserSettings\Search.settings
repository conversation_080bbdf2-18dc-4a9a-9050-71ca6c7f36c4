trackSelection = true
refreshSearchWindowsInPlayMode = false
pickerAdvancedUI = false
fetchPreview = true
defaultFlags = 0
keepOpen = false
queryFolder = "Assets"
onBoardingDoNotAskAgain = true
showPackageIndexes = false
showStatusBar = false
scopes = {
	"OpenInspectorPreview.66F56946" = "0"
	"currentGroup.66F56946" = "all"
	"picker_window_position_offset.075BCD15" = "-1169;173;1694;550"
	"picker_visibility_flags.075BCD15" = "264"
	"picker_item_size.075BCD15" = "22"
	"picker_inspector.075BCD15" = "0"
}
providers = {
	performance = {
		active = false
		priority = 100
		defaultAction = null
	}
	log = {
		active = false
		priority = 210
		defaultAction = null
	}
	scene = {
		active = true
		priority = 50
		defaultAction = null
	}
	adb = {
		active = false
		priority = 2500
		defaultAction = null
	}
	store = {
		active = false
		priority = 100
		defaultAction = null
	}
	asset = {
		active = true
		priority = 25
		defaultAction = null
	}
	profilermarkers = {
		active = false
		priority = 100
		defaultAction = null
	}
	find = {
		active = true
		priority = 25
		defaultAction = null
	}
	packages = {
		active = false
		priority = 90
		defaultAction = null
	}
	archetype = {
		active = true
		priority = 2500
		defaultAction = null
	}
	journal = {
		active = true
		priority = 2500
		defaultAction = null
	}
	presets_provider = {
		active = false
		priority = -10
		defaultAction = null
	}
}
objectSelectors = {
}
recentSearches = [
]
searchItemFavorites = [
]
savedSearchesSortOrder = 0
showSavedSearchPanel = false
hideTabs = false
expandedQueries = [
]
queryBuilder = true
ignoredProperties = "id;name;classname;imagecontentshash"
helperWidgetCurrentArea = "all"
disabledIndexers = ""
minIndexVariations = 2
findProviderIndexHelper = true
itemIconSize = 22