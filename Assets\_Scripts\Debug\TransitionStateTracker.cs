using UnityEngine;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using BTR;

namespace BTR.Debug
{
    /// <summary>
    /// Advanced logging and error detection system for scene transitions
    /// </summary>
    public class TransitionStateTracker : MonoBehaviour
    {
        [Header("Logging Settings")]
        [SerializeField] private bool enableFileLogging = true;
        [SerializeField] private bool enableConsoleLogging = true;
        [SerializeField] private bool enableErrorDetection = true;
        [SerializeField] private int maxLogEntries = 1000;
        
        [Header("Error Detection")]
        [SerializeField] private float progressStallThreshold = 10f; // seconds
        [SerializeField] private float memoryLeakThreshold = 100f; // MB increase
        [SerializeField] private float performanceDropThreshold = 0.5f; // 50% FPS drop
        
        // Logging
        private List<LogEntry> logEntries = new List<LogEntry>();
        private string logFilePath;
        private StringBuilder logBuffer = new StringBuilder();
        
        // Error Detection State
        private float lastProgressTime;
        private float lastProgressValue;
        private float baselineMemory;
        private float baselineFPS;
        private bool hasBaseline = false;
        
        // Performance Tracking
        private Queue<float> fpsHistory = new Queue<float>();
        private Queue<float> memoryHistory = new Queue<float>();
        private const int HISTORY_SIZE = 60; // 1 minute at 1 sample per second
        
        public static TransitionStateTracker Instance { get; private set; }
        
        // Events
        public event Action<ErrorType, string> OnErrorDetected;
        public event Action<string> OnPerformanceIssueDetected;
        
        public enum ErrorType
        {
            ProgressStall,
            MemoryLeak,
            PerformanceDrop,
            ComponentMissing,
            AsyncOperationStuck,
            EventSystemFailure
        }
        
        [System.Serializable]
        public class LogEntry
        {
            public DateTime timestamp;
            public string category;
            public string message;
            public LogLevel level;
            public float memoryUsage;
            public float fps;
            public string sceneName;
            public string transitionState;
            
            public LogEntry(string category, string message, LogLevel level, string sceneName = "", string transitionState = "")
            {
                this.timestamp = DateTime.Now;
                this.category = category;
                this.message = message;
                this.level = level;
                this.sceneName = sceneName;
                this.transitionState = transitionState;
                
                // Capture performance metrics
                this.memoryUsage = GC.GetTotalMemory(false) / (1024f * 1024f);
                this.fps = Time.unscaledDeltaTime > 0 ? 1f / Time.unscaledDeltaTime : 0f;
            }
            
            public override string ToString()
            {
                return $"[{timestamp:HH:mm:ss.fff}] [{level}] [{category}] {message} " +
                       $"(FPS: {fps:F0}, Mem: {memoryUsage:F1}MB, Scene: {sceneName}, State: {transitionState})";
            }
        }
        
        public enum LogLevel
        {
            Debug,
            Info,
            Warning,
            Error,
            Critical
        }
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeLogging();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeLogging()
        {
            // Setup log file path
            string logDirectory = Path.Combine(Application.persistentDataPath, "SceneTransitionLogs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }
            
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
            logFilePath = Path.Combine(logDirectory, $"SceneTransition_{timestamp}.log");
            
            Log("TransitionStateTracker", "Logging system initialized", LogLevel.Info);
            
            // Subscribe to diagnostic events if available
            if (SceneTransitionDiagnostic.Instance != null)
            {
                SceneTransitionDiagnostic.Instance.OnStateChanged += OnTransitionStateChanged;
                SceneTransitionDiagnostic.Instance.OnIssueDetected += OnIssueDetected;
            }
            
            // Start monitoring coroutine
            StartCoroutine(MonitoringCoroutine());
        }
        
        private void Start()
        {
            // Establish baseline performance metrics
            EstablishBaseline();
        }
        
        private void EstablishBaseline()
        {
            baselineMemory = GC.GetTotalMemory(false) / (1024f * 1024f);
            baselineFPS = Time.unscaledDeltaTime > 0 ? 1f / Time.unscaledDeltaTime : 60f;
            hasBaseline = true;
            
            Log("Performance", $"Baseline established - Memory: {baselineMemory:F1}MB, FPS: {baselineFPS:F0}", LogLevel.Info);
        }
        
        public void Log(string category, string message, LogLevel level = LogLevel.Info, string sceneName = "", string transitionState = "")
        {
            var entry = new LogEntry(category, message, level, sceneName, transitionState);
            logEntries.Add(entry);
            
            // Maintain log size
            if (logEntries.Count > maxLogEntries)
            {
                logEntries.RemoveAt(0);
            }
            
            // Console logging
            if (enableConsoleLogging)
            {
                switch (level)
                {
                    case LogLevel.Debug:
                    case LogLevel.Info:
                        Debug.Log($"[TransitionTracker] {entry}");
                        break;
                    case LogLevel.Warning:
                        Debug.LogWarning($"[TransitionTracker] {entry}");
                        break;
                    case LogLevel.Error:
                    case LogLevel.Critical:
                        Debug.LogError($"[TransitionTracker] {entry}");
                        break;
                }
            }
            
            // File logging
            if (enableFileLogging)
            {
                logBuffer.AppendLine(entry.ToString());
                
                // Flush buffer periodically
                if (logBuffer.Length > 1000)
                {
                    FlushLogBuffer();
                }
            }
        }
        
        private void FlushLogBuffer()
        {
            try
            {
                File.AppendAllText(logFilePath, logBuffer.ToString());
                logBuffer.Clear();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[TransitionTracker] Failed to write log file: {ex.Message}");
            }
        }
        
        private System.Collections.IEnumerator MonitoringCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(1f);
                
                if (!enableErrorDetection) continue;
                
                // Update performance history
                UpdatePerformanceHistory();
                
                // Check for various error conditions
                CheckForProgressStall();
                CheckForMemoryLeaks();
                CheckForPerformanceDrops();
                CheckForMissingComponents();
            }
        }
        
        private void UpdatePerformanceHistory()
        {
            float currentFPS = Time.unscaledDeltaTime > 0 ? 1f / Time.unscaledDeltaTime : 0f;
            float currentMemory = GC.GetTotalMemory(false) / (1024f * 1024f);
            
            fpsHistory.Enqueue(currentFPS);
            memoryHistory.Enqueue(currentMemory);
            
            if (fpsHistory.Count > HISTORY_SIZE)
            {
                fpsHistory.Dequeue();
                memoryHistory.Dequeue();
            }
        }
        
        private void CheckForProgressStall()
        {
            if (SceneTransitionDiagnostic.Instance == null) return;
            
            float currentProgress = SceneTransitionDiagnostic.Instance.CurrentProgress;
            float currentTime = Time.realtimeSinceStartup;
            
            // Check if progress has stalled
            if (Math.Abs(currentProgress - lastProgressValue) < 0.01f)
            {
                if (currentTime - lastProgressTime > progressStallThreshold)
                {
                    string error = $"Progress stalled at {currentProgress:P1} for {currentTime - lastProgressTime:F1} seconds";
                    Log("ErrorDetection", error, LogLevel.Error);
                    OnErrorDetected?.Invoke(ErrorType.ProgressStall, error);
                    
                    // Reset to avoid spam
                    lastProgressTime = currentTime;
                }
            }
            else
            {
                lastProgressValue = currentProgress;
                lastProgressTime = currentTime;
            }
        }
        
        private void CheckForMemoryLeaks()
        {
            if (!hasBaseline || memoryHistory.Count < 10) return;
            
            float currentMemory = memoryHistory.ToArray()[memoryHistory.Count - 1];
            float memoryIncrease = currentMemory - baselineMemory;
            
            if (memoryIncrease > memoryLeakThreshold)
            {
                string error = $"Potential memory leak detected - increased by {memoryIncrease:F1}MB from baseline";
                Log("ErrorDetection", error, LogLevel.Warning);
                OnErrorDetected?.Invoke(ErrorType.MemoryLeak, error);
                
                // Update baseline to avoid spam
                baselineMemory = currentMemory;
            }
        }
        
        private void CheckForPerformanceDrops()
        {
            if (!hasBaseline || fpsHistory.Count < 10) return;
            
            float averageFPS = 0f;
            foreach (float fps in fpsHistory)
            {
                averageFPS += fps;
            }
            averageFPS /= fpsHistory.Count;
            
            float performanceDrop = (baselineFPS - averageFPS) / baselineFPS;
            
            if (performanceDrop > performanceDropThreshold)
            {
                string warning = $"Performance drop detected - FPS dropped {performanceDrop:P0} from baseline ({baselineFPS:F0} -> {averageFPS:F0})";
                Log("ErrorDetection", warning, LogLevel.Warning);
                OnPerformanceIssueDetected?.Invoke(warning);
            }
        }
        
        private void CheckForMissingComponents()
        {
            // Check for critical components
            if (SceneTransitionDiagnostic.Instance == null)
            {
                Log("ErrorDetection", "SceneTransitionDiagnostic component missing", LogLevel.Error);
                OnErrorDetected?.Invoke(ErrorType.ComponentMissing, "SceneTransitionDiagnostic component missing");
            }
            
            if (FindFirstObjectByType<SceneManagerBTR>() == null)
            {
                Log("ErrorDetection", "SceneManagerBTR component missing", LogLevel.Error);
                OnErrorDetected?.Invoke(ErrorType.ComponentMissing, "SceneManagerBTR component missing");
            }
            
            if (LoadingScreen.Instance == null)
            {
                Log("ErrorDetection", "LoadingScreen instance missing", LogLevel.Warning);
            }
        }
        
        // Event Handlers
        private void OnTransitionStateChanged(SceneTransitionDiagnostic.TransitionState newState)
        {
            string sceneName = SceneTransitionDiagnostic.Instance?.CurrentSceneName ?? "";
            Log("StateChange", $"Transition state changed to {newState}", LogLevel.Info, sceneName, newState.ToString());
        }
        
        private void OnIssueDetected(string issue)
        {
            Log("IssueDetected", issue, LogLevel.Error);
        }
        
        // Public Methods
        public List<LogEntry> GetLogEntries(LogLevel minLevel = LogLevel.Debug)
        {
            return logEntries.FindAll(entry => entry.level >= minLevel);
        }
        
        public void ExportLogsToFile(string customPath = null)
        {
            string exportPath = customPath ?? Path.Combine(Application.persistentDataPath, $"SceneTransition_Export_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.log");
            
            try
            {
                var exportBuilder = new StringBuilder();
                exportBuilder.AppendLine("=== Scene Transition Diagnostic Log Export ===");
                exportBuilder.AppendLine($"Export Time: {DateTime.Now}");
                exportBuilder.AppendLine($"Total Entries: {logEntries.Count}");
                exportBuilder.AppendLine();
                
                foreach (var entry in logEntries)
                {
                    exportBuilder.AppendLine(entry.ToString());
                }
                
                File.WriteAllText(exportPath, exportBuilder.ToString());
                Log("Export", $"Logs exported to {exportPath}", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Log("Export", $"Failed to export logs: {ex.Message}", LogLevel.Error);
            }
        }
        
        private void OnDestroy()
        {
            // Flush any remaining logs
            FlushLogBuffer();
            
            // Unsubscribe from events
            if (SceneTransitionDiagnostic.Instance != null)
            {
                SceneTransitionDiagnostic.Instance.OnStateChanged -= OnTransitionStateChanged;
                SceneTransitionDiagnostic.Instance.OnIssueDetected -= OnIssueDetected;
            }
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                FlushLogBuffer();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                FlushLogBuffer();
            }
        }
    }
}
