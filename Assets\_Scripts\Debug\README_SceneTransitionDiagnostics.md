# Scene Transition Diagnostic System

A comprehensive diagnostic and recovery system for monitoring and troubleshooting scene transitions in your Unity project.

## Overview

This system provides real-time monitoring, automatic error detection, and recovery mechanisms for scene transitions that may get stuck or fail. It integrates seamlessly with your existing SceneManagerBTR and event system.

**Namespace:** `BTR.SceneTransition.Debug` - This avoids conflicts with the legacy BTR.Debug system that was removed during debug migration.

## Components

### 1. SceneTransitionDiagnostic.cs

**Main diagnostic component** that monitors all aspects of scene transitions.

**Features:**

- Real-time state tracking
- Progress monitoring
- Stuck transition detection
- Automatic recovery attempts
- Performance metrics collection
- Event logging

**Key Properties:**

- `CurrentState` - Current transition state
- `CurrentSceneName` - Scene being loaded
- `TransitionDuration` - Time elapsed
- `CurrentProgress` - Loading progress (0-1)
- `IsStuck` - Whether transition is stuck

### 2. SceneTransitionMonitorUI.cs

**Real-time monitoring interface** displayed on-screen during gameplay.

**Features:**

- Live state display
- Progress bars
- Performance metrics
- Manual intervention buttons
- Event log viewer
- Draggable window interface

**Controls:**

- **F12** - Toggle UI visibility
- **Reset State** - Force reset transition state
- **Force Load** - Manually trigger scene load
- **Clear Log** - Clear event history

### 3. TransitionStateTracker.cs

**Advanced logging and error detection system**.

**Features:**

- File and console logging
- Performance history tracking
- Automatic error detection
- Memory leak detection
- FPS drop monitoring
- Log export functionality

**Error Detection:**

- Progress stall detection (10s threshold)
- Memory leak detection (100MB threshold)
- Performance drop detection (50% FPS drop)
- Missing component detection

### 4. SceneTransitionRecovery.cs

**Automatic recovery and manual intervention tools**.

**Recovery Methods:**

1. **ForceSceneLoad** - Retry current scene load
2. **RestartLoadingScreen** - Restart loading screen fade
3. **ClearAsyncOperations** - Force garbage collection
4. **ResetSceneManager** - Reset scene manager state
5. **EmergencySceneLoad** - Load fallback scene
6. **FullSystemReset** - Complete system reset

**Auto Recovery:**

- Triggered when transitions get stuck
- Progressive recovery attempts (3 max)
- Configurable delays and thresholds

### 5. SceneTransitionDiagnosticSetup.cs

**Setup and validation script** for easy system configuration.

**Features:**

- One-click system setup
- Component validation
- Build configuration options
- Test scene transition functionality

### 6. BTRDebugCompatibility.cs

**Compatibility layer** for legacy BTR.Debug calls.

**Purpose:**

- Provides missing `BTR.Debug.Log` and `BTR.Debug.LogError` methods
- Forwards all calls to Unity's standard Debug system
- Prevents compilation errors from legacy code
- No performance impact - uses conditional compilation

## Setup Instructions

### Quick Setup

1. Add `SceneTransitionDiagnosticSetup` to any GameObject in your base scene
2. Check "Auto Setup On Awake" in the inspector
3. Play the scene - the system will auto-configure itself

### Manual Setup

1. Create the diagnostic components manually:

   ```csharp
   // In your initialization code
   var setup = FindFirstObjectByType<SceneTransitionDiagnosticSetup>();
   setup.SetupDiagnosticSystem();
   ```

2. Or use the context menu:
   - Right-click on SceneTransitionDiagnosticSetup component
   - Select "Setup Diagnostic System"

### Validation

Use the "Validate System" context menu to check if all components are properly configured.

## Usage

### Monitoring Scene Transitions

1. **Real-time Monitoring**: Press F12 to show/hide the monitoring UI
2. **Console Logs**: Check Unity console for detailed transition logs
3. **File Logs**: Logs are saved to `Application.persistentDataPath/SceneTransitionLogs/`

### When Transitions Get Stuck

The system will automatically:

1. Detect stuck transitions (after 30 seconds by default)
2. Attempt automatic recovery (up to 3 attempts)
3. Log all recovery attempts
4. Provide manual intervention options

### Manual Intervention

If automatic recovery fails:

1. **Open Monitor UI** (F12)
2. **Use Control Buttons**:
   - "Reset State" - Reset diagnostic state
   - "Force Load" - Manually trigger scene load
3. **Context Menu Options**:
   - Right-click components for additional recovery options

### Emergency Recovery

If all else fails:

1. Use "Manual Emergency Reset" context menu
2. This will load the fallback scene (default: "Ouroboros - Base")
3. All systems will be reset to clean state

## Configuration

### SceneTransitionDiagnostic Settings

- `enableDiagnostics` - Master enable/disable
- `enableDetailedLogging` - Console logging
- `enablePerformanceMonitoring` - Performance tracking
- `stuckDetectionTimeout` - Time before considering stuck (30s)
- `enableAutoRecovery` - Automatic recovery attempts
- `maxRecoveryAttempts` - Maximum auto recovery tries (3)

### TransitionStateTracker Settings

- `enableFileLogging` - Save logs to file
- `progressStallThreshold` - Progress stall detection (10s)
- `memoryLeakThreshold` - Memory increase threshold (100MB)
- `performanceDropThreshold` - FPS drop threshold (50%)

### SceneTransitionRecovery Settings

- `enableAutoRecovery` - Auto recovery on stuck transitions
- `autoRecoveryDelay` - Delay before recovery attempt (5s)
- `fallbackSceneName` - Emergency fallback scene
- `enableEmergencyReset` - Allow emergency scene loads

## Integration with Existing Systems

The diagnostic system integrates with:

- **SceneManagerBTR** - Monitors scene loading operations
- **SceneEvents** - Subscribes to scene transition events
- **GameEvents** - Monitors game state changes
- **LoadingScreen** - Tracks loading screen operations

No modifications to existing code are required.

## Performance Impact

- **Minimal overhead** - Only active during scene transitions
- **Efficient logging** - Buffered file writes
- **Smart monitoring** - 1-second update intervals
- **Build optimization** - Can be disabled for release builds

## Troubleshooting

### Common Issues

**"SceneManagerBTR not found"**

- Ensure SceneManagerBTR is in your base scene
- Check execution order (diagnostic should initialize early)

**"UI not showing"**

- Press F12 to toggle visibility
- Check `enableUIDisplay` setting
- Ensure SceneTransitionMonitorUI component exists

**"Logs not saving"**

- Check write permissions to persistent data path
- Verify `enableFileLogging` is true
- Check Unity console for file write errors

**"Recovery not working"**

- Verify `enableAutoRecovery` is true
- Check if maximum recovery attempts reached
- Try manual recovery methods

### Debug Commands

**Console Commands:**

```csharp
// Force reset diagnostic state
SceneTransitionDiagnostic.Instance.ForceResetTransitionState();

// Export logs
TransitionStateTracker.Instance.ExportLogsToFile();

// Manual recovery
SceneTransitionRecovery.Instance.ManualEmergencyReset();
```

**Context Menu Options:**

- All components have context menu options for testing
- Use "Validate System" to check component status
- Use "Test Scene Transition" to trigger a test transition

## Log File Locations

- **Windows**: `%USERPROFILE%\AppData\LocalLow\[CompanyName]\[ProductName]\SceneTransitionLogs\`
- **Mac**: `~/Library/Application Support/[CompanyName]/[ProductName]/SceneTransitionLogs/`
- **Linux**: `~/.config/unity3d/[CompanyName]/[ProductName]/SceneTransitionLogs/`

## Support

For issues or questions:

1. Check Unity console for error messages
2. Review log files for detailed information
3. Use "Validate System" to check component status
4. Try manual recovery methods if automatic recovery fails

The diagnostic system is designed to be self-contained and should not interfere with normal game operation.
