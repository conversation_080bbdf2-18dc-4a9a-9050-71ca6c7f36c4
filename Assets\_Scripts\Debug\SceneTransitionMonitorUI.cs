using UnityEngine;
using System.Linq;
using BTR.SceneTransition.Debug;

namespace BTR.SceneTransition.Debug
{
    /// <summary>
    /// Real-time UI for monitoring scene transitions and providing manual intervention tools
    /// </summary>
    public class SceneTransitionMonitorUI : MonoBehaviour
    {
        [Header("UI Settings")]
        [SerializeField] private bool showUI = true;
        [SerializeField] private bool showDetailedLog = false;
        [SerializeField] private bool showPerformanceMetrics = true;
        [SerializeField] private KeyCode toggleKey = KeyCode.F12;

        [Header("UI Layout")]
        [SerializeField] private float windowWidth = 400f;
        [SerializeField] private float windowHeight = 600f;
        [SerializeField] private float margin = 10f;

        // UI State
        private Vector2 scrollPosition = Vector2.zero;
        private Vector2 logScrollPosition = Vector2.zero;
        private bool isExpanded = true;
        private Rect windowRect;

        // Cached references
        private SceneTransitionDiagnostic diagnostic;
        private GUIStyle headerStyle;
        private GUIStyle statusStyle;
        private GUIStyle buttonStyle;
        private GUIStyle logStyle;
        private bool stylesInitialized = false;

        private void Awake()
        {
            // Position window in top-right corner
            windowRect = new Rect(Screen.width - windowWidth - margin, margin, windowWidth, windowHeight);
        }

        private void Start()
        {
            // Find the diagnostic component
            diagnostic = SceneTransitionDiagnostic.Instance;
            if (diagnostic == null)
            {
                Debug.LogWarning("[SceneTransitionMonitorUI] SceneTransitionDiagnostic not found!");
            }
        }

        private void Update()
        {
            // Toggle UI visibility
            if (Input.GetKeyDown(toggleKey))
            {
                showUI = !showUI;
            }

            // Update window position if screen size changes
            if (windowRect.x + windowRect.width > Screen.width)
            {
                windowRect.x = Screen.width - windowWidth - margin;
            }
            if (windowRect.y + windowRect.height > Screen.height)
            {
                windowRect.y = Screen.height - windowHeight - margin;
            }
        }

        private void OnGUI()
        {
            if (!showUI || diagnostic == null) return;

            InitializeStyles();

            // Make window draggable
            windowRect = GUI.Window(12345, windowRect, DrawWindow, "Scene Transition Monitor", GUI.skin.window);
        }

        private void InitializeStyles()
        {
            if (stylesInitialized) return;

            headerStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 14,
                fontStyle = FontStyle.Bold,
                normal = { textColor = Color.white }
            };

            statusStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 12,
                normal = { textColor = Color.cyan }
            };

            buttonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 11
            };

            logStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 10,
                wordWrap = true,
                normal = { textColor = Color.white }
            };

            stylesInitialized = true;
        }

        private void DrawWindow(int windowID)
        {
            GUILayout.BeginVertical();

            // Header with toggle
            GUILayout.BeginHorizontal();
            GUILayout.Label("Scene Transition Monitor", headerStyle);
            GUILayout.FlexibleSpace();
            if (GUILayout.Button(isExpanded ? "▼" : "▶", GUILayout.Width(20)))
            {
                isExpanded = !isExpanded;
            }
            GUILayout.EndHorizontal();

            if (!isExpanded)
            {
                GUILayout.EndVertical();
                GUI.DragWindow();
                return;
            }

            // Current Status
            DrawCurrentStatus();

            GUILayout.Space(10);

            // Control Buttons
            DrawControlButtons();

            GUILayout.Space(10);

            // Performance Metrics
            if (showPerformanceMetrics)
            {
                DrawPerformanceMetrics();
                GUILayout.Space(10);
            }

            // Event Log
            if (showDetailedLog)
            {
                DrawEventLog();
            }

            // Settings
            DrawSettings();

            GUILayout.EndVertical();
            GUI.DragWindow();
        }

        private void DrawCurrentStatus()
        {
            GUILayout.Label("Current Status", headerStyle);

            // State
            Color stateColor = GetStateColor(diagnostic.CurrentState);
            GUI.color = stateColor;
            GUILayout.Label($"State: {diagnostic.CurrentState}", statusStyle);
            GUI.color = Color.white;

            // Scene Name
            if (!string.IsNullOrEmpty(diagnostic.CurrentSceneName))
            {
                GUILayout.Label($"Scene: {diagnostic.CurrentSceneName}", statusStyle);
            }

            // Progress
            if (diagnostic.CurrentProgress > 0f)
            {
                GUILayout.Label($"Progress: {diagnostic.CurrentProgress:P1}", statusStyle);

                // Progress bar
                Rect progressRect = GUILayoutUtility.GetRect(windowWidth - 40, 20);
                GUI.Box(progressRect, "");
                Rect fillRect = new Rect(progressRect.x + 2, progressRect.y + 2,
                    (progressRect.width - 4) * diagnostic.CurrentProgress, progressRect.height - 4);
                GUI.Box(fillRect, "", GUI.skin.button);
            }

            // Duration
            if (diagnostic.TransitionDuration > 0f)
            {
                GUILayout.Label($"Duration: {diagnostic.TransitionDuration:F1}s", statusStyle);
            }

            // Stuck indicator
            if (diagnostic.IsStuck)
            {
                GUI.color = Color.red;
                GUILayout.Label("⚠ TRANSITION STUCK ⚠", headerStyle);
                GUI.color = Color.white;
            }
        }

        private void DrawControlButtons()
        {
            GUILayout.Label("Manual Controls", headerStyle);

            GUILayout.BeginHorizontal();

            if (GUILayout.Button("Reset State", buttonStyle))
            {
                diagnostic.ForceResetTransitionState();
            }

            if (GUILayout.Button("Force Load", buttonStyle))
            {
                diagnostic.ForceSceneLoad();
            }

            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();

            if (GUILayout.Button("Clear Log", buttonStyle))
            {
                diagnostic.ClearEventLog();
            }

            if (GUILayout.Button("Refresh", buttonStyle))
            {
                // Force UI refresh - no action needed
            }

            GUILayout.EndHorizontal();
        }

        private void DrawPerformanceMetrics()
        {
            GUILayout.Label("Performance", headerStyle);

            // Memory usage
            long memoryUsage = System.GC.GetTotalMemory(false);
            float memoryMB = memoryUsage / (1024f * 1024f);
            GUILayout.Label($"Memory: {memoryMB:F1} MB", statusStyle);

            // Frame rate
            float fps = 1f / Time.unscaledDeltaTime;
            Color fpsColor = fps > 30f ? Color.green : (fps > 15f ? Color.yellow : Color.red);
            GUI.color = fpsColor;
            GUILayout.Label($"FPS: {fps:F0}", statusStyle);
            GUI.color = Color.white;

            // Scene count
            int sceneCount = UnityEngine.SceneManagement.SceneManager.sceneCount;
            GUILayout.Label($"Loaded Scenes: {sceneCount}", statusStyle);
        }

        private void DrawEventLog()
        {
            GUILayout.Label("Event Log", headerStyle);

            var eventLog = diagnostic.EventLog;
            if (eventLog.Count == 0)
            {
                GUILayout.Label("No events logged", statusStyle);
                return;
            }

            // Scrollable log area
            logScrollPosition = GUILayout.BeginScrollView(logScrollPosition, GUILayout.Height(150));

            // Show last 20 events
            var recentEvents = eventLog.TakeLast(20);
            foreach (string logEntry in recentEvents)
            {
                // Color code log entries
                if (logEntry.Contains("[Error]"))
                    GUI.color = Color.red;
                else if (logEntry.Contains("[Warning]"))
                    GUI.color = Color.yellow;
                else
                    GUI.color = Color.white;

                GUILayout.Label(logEntry, logStyle);
            }

            GUI.color = Color.white;
            GUILayout.EndScrollView();
        }

        private void DrawSettings()
        {
            GUILayout.Label("Settings", headerStyle);

            showDetailedLog = GUILayout.Toggle(showDetailedLog, "Show Event Log");
            showPerformanceMetrics = GUILayout.Toggle(showPerformanceMetrics, "Show Performance");

            GUILayout.Label($"Toggle Key: {toggleKey}", statusStyle);
        }

        private Color GetStateColor(SceneTransitionDiagnostic.TransitionState state)
        {
            switch (state)
            {
                case SceneTransitionDiagnostic.TransitionState.Idle:
                    return Color.gray;
                case SceneTransitionDiagnostic.TransitionState.LoadRequested:
                case SceneTransitionDiagnostic.TransitionState.LoadStarted:
                case SceneTransitionDiagnostic.TransitionState.LoadInProgress:
                    return Color.yellow;
                case SceneTransitionDiagnostic.TransitionState.LoadCompleted:
                case SceneTransitionDiagnostic.TransitionState.FadeOutCompleted:
                    return Color.green;
                case SceneTransitionDiagnostic.TransitionState.Stuck:
                case SceneTransitionDiagnostic.TransitionState.Error:
                    return Color.red;
                case SceneTransitionDiagnostic.TransitionState.Recovering:
                    return Color.magenta;
                default:
                    return Color.cyan;
            }
        }
    }
}
