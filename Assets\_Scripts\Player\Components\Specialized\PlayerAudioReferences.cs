using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using FMODUnity;
using BTR.Player;

namespace BTR.Player
{
    /// <summary>
    /// Centralized singleton for all player audio management and sound effects.
    /// Handles FMOD event pooling and provides a unified API for all player components.
    /// </summary>
    public class PlayerAudioReferences : PlayerComponent, IStatefulPlayerComponent
    {
        #region Singleton
        public static PlayerAudioReferences Instance { get; private set; }

        protected override void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                base.Awake();
            }
            else if (Instance != this)
            {
                Destroy(gameObject);
                return;
            }
        }

        protected override void OnDestroy()
        {
            if (Instance == this)
            {
                // Clean up audio pools and active events
                StopAllAudio();
                Instance = null;
            }
            base.OnDestroy();
        }
        #endregion

        [Header("Audio Settings")]
        [SerializeField] private bool debugAudio = false;

        [Header("Combat Audio")]
        [SerializeField] private EventReference shootEvent;
        [SerializeField] private EventReference randomShootingEvent;
        [SerializeField] private EventReference shootTagEvent;
        [SerializeField] private EventReference reloadEvent;
        [SerializeField] private EventReference lockTargetEvent;
        [SerializeField] private EventReference dodgeEvent;

        [Header("Health Audio")]
        [SerializeField] private EventReference damageEvent;
        [SerializeField] private EventReference deathEvent;
        [SerializeField] private EventReference healEvent;

        [Header("Time Control Audio")]
        [SerializeField] private EventReference rewindEvent;
        [SerializeField] private EventReference slowTimeEvent;
        [SerializeField] private EventReference timeRestoreEvent;
        
        [Header("Detection Audio")]
        [SerializeField] private EventReference projectileDetectionEvent;



        [Header("Audio Pooling Settings")]
        [SerializeField] private int maxConcurrentShootSounds = 3;
        [SerializeField] private float minTimeBetweenShootSounds = 0.05f;

        // Component references
        private PlayerShootingComponent shootingComponent;
        private PlayerLockingComponent lockingComponent;
        private PlayerDodgeComponent dodgeComponent;
        private PlayerHealthComponent healthComponent;
        private PlayerTimeControlComponent timeControlComponent;

        // Audio state
        private Dictionary<string, FMOD.Studio.EventInstance> activeEvents;

        // Audio pooling for shooting sounds
        private Queue<FMOD.Studio.EventInstance> shootSoundPool;
        private float lastShootSoundTime;

        #region PlayerComponent Overrides

        protected override void OnComponentInitialize()
        {
            InitializeReferences();
            InitializeAudioState();
        }

        protected override void OnComponentStarted()
        {
            ValidateAudioEvents();
        }

        public override void OnComponentUpdate()
        {
            // No continuous audio updates needed
        }

        protected override void OnComponentStopped()
        {
            UnsubscribeFromEvents();
            StopAllAudio();
        }

        protected override bool OnComponentValidate()
        {
            // Audio component is optional, so always return true
            return true;
        }

        #endregion

        #region Initialization

        private void InitializeReferences()
        {
            shootingComponent = GetComponent<PlayerShootingComponent>();
            lockingComponent = GetComponent<PlayerLockingComponent>();
            dodgeComponent = GetComponent<PlayerDodgeComponent>();
            healthComponent = GetComponent<PlayerHealthComponent>();
            timeControlComponent = GetComponent<PlayerTimeControlComponent>();
        }

        private void InitializeAudioState()
        {
            activeEvents = new Dictionary<string, FMOD.Studio.EventInstance>();
            shootSoundPool = new Queue<FMOD.Studio.EventInstance>();
        }

        #endregion

        protected override void Start()
        {
            base.Start();

            // Subscribe to GameEvents for PLAYER audio responses only
            if (GameEventsManager.Instance != null && GameEventsManager.Instance.Events != null)
            {
                var gameEvents = GameEventsManager.Instance.Events;

                // Player health events
                gameEvents.OnPlayerDamaged += (damage) => PlayDamage();
                gameEvents.OnPlayerDeath += () => PlayDeath();

                // Player action events
                gameEvents.OnPlayerShoot += () => PlayShoot();
                gameEvents.OnPlayerDodge += () => PlayDodge();
                gameEvents.OnPlayerLockTarget += () => PlayLockTarget();
                gameEvents.OnPlayerReload += () => PlayReload();
                
                // Player detection events
                gameEvents.OnProjectileDetectionStart += () => PlayProjectileDetectionStart();
                gameEvents.OnProjectileDetectionStop += () => PlayProjectileDetectionStop();

                LogDebug("Subscribed to GameEvents for PLAYER audio responses");
            }
            else
            {
                Debug.LogWarning($"[{GetType().Name}] GameEventsManager not available - player audio may not play");
            }

            // Fallback: Subscribe to health events directly if GameEvents fails
            if (healthComponent != null)
            {
                healthComponent.OnDamageReceived += OnDamageReceived;
                healthComponent.OnDeath += OnPlayerDeath;
                healthComponent.OnHealthChanged += OnHealthChanged;
            }
        }

        private void UnsubscribeFromEvents()
        {
            // Unsubscribe from component events
            if (healthComponent != null)
            {
                healthComponent.OnDamageReceived -= OnDamageReceived;
                healthComponent.OnDeath -= OnPlayerDeath;
                healthComponent.OnHealthChanged -= OnHealthChanged;
            }
        }

        private void ValidateAudioEvents()
        {
            if (debugAudio)
            {
                LogDebug("Validating audio events...");
                if (shootEvent.IsNull) LogWarning("Shoot event not assigned");
                if (damageEvent.IsNull) LogWarning("Damage event not assigned");
            }
        }


        #region Audio Playback

        private void PlayOneShot(EventReference eventRef, Vector3 position = default)
        {
            if (eventRef.IsNull)
                return;

            if (position == default)
            {
                position = transform.position;
            }

            RuntimeManager.PlayOneShotAttached(eventRef, gameObject);

            if (debugAudio)
            {
                LogDebug($"Played audio event: {eventRef.Path}");
            }
        }

        private FMOD.Studio.EventInstance CreateAndPlayEvent(EventReference eventRef, string key = null)
        {
            if (eventRef.IsNull)
                return default;

            var instance = RuntimeManager.CreateInstance(eventRef);
            instance.set3DAttributes(RuntimeUtils.To3DAttributes(transform));
            instance.start();

            if (!string.IsNullOrEmpty(key))
            {
                if (activeEvents.ContainsKey(key))
                {
                    activeEvents[key].stop(FMOD.Studio.STOP_MODE.ALLOWFADEOUT);
                    activeEvents[key].release();
                }
                activeEvents[key] = instance;
            }

            if (debugAudio)
            {
                LogDebug($"Created and played audio event: {eventRef.Path}");
            }

            return instance;
        }

        private void StopEvent(string key)
        {
            if (activeEvents.ContainsKey(key))
            {
                activeEvents[key].stop(FMOD.Studio.STOP_MODE.ALLOWFADEOUT);
                activeEvents[key].release();
                activeEvents.Remove(key);

                if (debugAudio)
                {
                    LogDebug($"Stopped audio event: {key}");
                }
            }
        }

        private void StopAllAudio()
        {
            foreach (var kvp in activeEvents)
            {
                kvp.Value.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
                kvp.Value.release();
            }
            activeEvents.Clear();

            if (debugAudio)
            {
                LogDebug("Stopped all audio events");
            }
        }

        #endregion

        #region Specific Audio Methods



        public void PlayShoot()
        {
            PlayOneShot(shootEvent);
        }

        public void PlayShootingSound()
        {
            PlayShoot();
        }

        public void PlayReload()
        {
            PlayOneShot(reloadEvent);
        }

        public void PlayLockTarget()
        {
            PlayOneShot(lockTargetEvent);
        }

        public void PlayDodge()
        {
            PlayOneShot(dodgeEvent);
        }

        public void PlayDamage()
        {
            PlayOneShot(damageEvent);
        }

        public void PlayDeath()
        {
            PlayOneShot(deathEvent);
        }

        public void PlayHeal()
        {
            PlayOneShot(healEvent);
        }

        public void PlayRewind()
        {
            CreateAndPlayEvent(rewindEvent, "rewind");
        }

        public void PlaySlowTime()
        {
            CreateAndPlayEvent(slowTimeEvent, "slowtime");
        }

        public void PlayTimeRestore()
        {
            PlayOneShot(timeRestoreEvent);
            StopEvent("rewind");
            StopEvent("slowtime");
        }
        
        public void PlayProjectileDetectionStart()
        {
            CreateAndPlayEvent(projectileDetectionEvent, "projectiledetection");
        }
        
        public void PlayProjectileDetectionStop()
        {
            StopEvent("projectiledetection");
        }

        #endregion

        #region Event Handlers



        private void OnDamageReceived(float damage)
        {
            PlayDamage();
        }

        private void OnPlayerDeath()
        {
            PlayDeath();
            StopAllAudio(); // Stop all other audio when player dies
        }

        private void OnHealthChanged(float newHealth)
        {
            // Could play heal sound if health increased
            // This would need additional logic to determine if it's healing vs damage
        }

        #endregion

        #region IStatefulPlayerComponent Implementation

        public void OnPlayerStateChanged(PlayerState oldState, PlayerState newState)
        {
            switch (newState)
            {
                case PlayerState.Dead:
                    StopAllAudio();
                    break;

                case PlayerState.TimeControl:
                    if (timeControlComponent != null)
                    {
                        if (timeControlComponent.IsRewinding())
                        {
                            PlayRewind();
                        }
                        else if (timeControlComponent.IsSlowingTime())
                        {
                            PlaySlowTime();
                        }
                    }
                    break;

                case PlayerState.Active:
                    if (oldState == PlayerState.TimeControl)
                    {
                        PlayTimeRestore();
                    }
                    break;

                case PlayerState.Dodging:
                    PlayDodge();
                    break;
            }
        }

        public bool CanTransitionToState(PlayerState targetState)
        {
            // Audio component doesn't restrict state transitions
            return true;
        }

        #endregion

        #region Public API



        /// <summary>
        /// Play a custom audio event
        /// </summary>
        /// <param name="eventRef">FMOD event reference</param>
        /// <param name="position">World position (optional)</param>
        public void PlayCustomEvent(EventReference eventRef, Vector3 position = default)
        {
            PlayOneShot(eventRef, position);
        }

        /// <summary>
        /// Check if an audio event is currently playing
        /// </summary>
        /// <param name="key">Event key</param>
        /// <returns>True if playing</returns>
        public bool IsEventPlaying(string key)
        {
            if (activeEvents.ContainsKey(key))
            {
                activeEvents[key].getPlaybackState(out FMOD.Studio.PLAYBACK_STATE state);
                return state == FMOD.Studio.PLAYBACK_STATE.PLAYING;
            }
            return false;
        }

        #endregion
    }
}
