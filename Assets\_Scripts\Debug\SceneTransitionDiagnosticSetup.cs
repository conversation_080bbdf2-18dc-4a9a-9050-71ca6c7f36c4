using UnityEngine;
using BTR.Debug;

namespace BTR.Debug
{
    /// <summary>
    /// Setup script to automatically configure the scene transition diagnostic system
    /// </summary>
    [DefaultExecutionOrder(-200)] // Initialize very early
    public class SceneTransitionDiagnosticSetup : MonoBehaviour
    {
        [Header("Auto Setup")]
        [SerializeField] private bool autoSetupOnAwake = true;
        [SerializeField] private bool createDiagnosticComponents = true;
        [SerializeField] private bool createMonitoringUI = true;
        [SerializeField] private bool createStateTracker = true;
        [SerializeField] private bool createRecoverySystem = true;
        
        [Header("Configuration")]
        [SerializeField] private bool enableDiagnosticsInBuild = false;
        [SerializeField] private bool enableUIInBuild = false;
        
        private static bool hasBeenSetup = false;
        
        private void Awake()
        {
            // Only setup once per session
            if (hasBeenSetup) 
            {
                Destroy(gameObject);
                return;
            }
            
            if (autoSetupOnAwake)
            {
                SetupDiagnosticSystem();
                hasBeenSetup = true;
            }
        }
        
        [ContextMenu("Setup Diagnostic System")]
        public void SetupDiagnosticSystem()
        {
            Debug.Log("[SceneTransitionDiagnosticSetup] Setting up scene transition diagnostic system...");
            
            // Check if we should enable diagnostics in build
            bool enableInCurrentBuild = Application.isEditor || enableDiagnosticsInBuild;
            bool enableUIInCurrentBuild = Application.isEditor || enableUIInBuild;
            
            if (!enableInCurrentBuild)
            {
                Debug.Log("[SceneTransitionDiagnosticSetup] Diagnostics disabled for builds");
                return;
            }
            
            // Create diagnostic components
            if (createDiagnosticComponents)
            {
                SetupDiagnosticComponent();
            }
            
            if (createStateTracker)
            {
                SetupStateTracker();
            }
            
            if (createRecoverySystem)
            {
                SetupRecoverySystem();
            }
            
            if (createMonitoringUI && enableUIInCurrentBuild)
            {
                SetupMonitoringUI();
            }
            
            Debug.Log("[SceneTransitionDiagnosticSetup] Diagnostic system setup complete!");
        }
        
        private void SetupDiagnosticComponent()
        {
            if (SceneTransitionDiagnostic.Instance != null)
            {
                Debug.Log("[SceneTransitionDiagnosticSetup] SceneTransitionDiagnostic already exists");
                return;
            }
            
            GameObject diagnosticGO = new GameObject("SceneTransitionDiagnostic");
            DontDestroyOnLoad(diagnosticGO);
            
            var diagnostic = diagnosticGO.AddComponent<SceneTransitionDiagnostic>();
            Debug.Log("[SceneTransitionDiagnosticSetup] ✅ SceneTransitionDiagnostic created");
        }
        
        private void SetupStateTracker()
        {
            if (TransitionStateTracker.Instance != null)
            {
                Debug.Log("[SceneTransitionDiagnosticSetup] TransitionStateTracker already exists");
                return;
            }
            
            GameObject trackerGO = new GameObject("TransitionStateTracker");
            DontDestroyOnLoad(trackerGO);
            
            var tracker = trackerGO.AddComponent<TransitionStateTracker>();
            Debug.Log("[SceneTransitionDiagnosticSetup] ✅ TransitionStateTracker created");
        }
        
        private void SetupRecoverySystem()
        {
            if (SceneTransitionRecovery.Instance != null)
            {
                Debug.Log("[SceneTransitionDiagnosticSetup] SceneTransitionRecovery already exists");
                return;
            }
            
            GameObject recoveryGO = new GameObject("SceneTransitionRecovery");
            DontDestroyOnLoad(recoveryGO);
            
            var recovery = recoveryGO.AddComponent<SceneTransitionRecovery>();
            Debug.Log("[SceneTransitionDiagnosticSetup] ✅ SceneTransitionRecovery created");
        }
        
        private void SetupMonitoringUI()
        {
            if (FindFirstObjectByType<SceneTransitionMonitorUI>() != null)
            {
                Debug.Log("[SceneTransitionDiagnosticSetup] SceneTransitionMonitorUI already exists");
                return;
            }
            
            GameObject uiGO = new GameObject("SceneTransitionMonitorUI");
            DontDestroyOnLoad(uiGO);
            
            var monitorUI = uiGO.AddComponent<SceneTransitionMonitorUI>();
            Debug.Log("[SceneTransitionDiagnosticSetup] ✅ SceneTransitionMonitorUI created");
        }
        
        [ContextMenu("Validate System")]
        public void ValidateSystem()
        {
            Debug.Log("=== Scene Transition Diagnostic System Validation ===");
            
            // Check core components
            bool diagnosticExists = SceneTransitionDiagnostic.Instance != null;
            bool trackerExists = TransitionStateTracker.Instance != null;
            bool recoveryExists = SceneTransitionRecovery.Instance != null;
            bool uiExists = FindFirstObjectByType<SceneTransitionMonitorUI>() != null;
            
            Debug.Log($"SceneTransitionDiagnostic: {(diagnosticExists ? "✅" : "❌")}");
            Debug.Log($"TransitionStateTracker: {(trackerExists ? "✅" : "❌")}");
            Debug.Log($"SceneTransitionRecovery: {(recoveryExists ? "✅" : "❌")}");
            Debug.Log($"SceneTransitionMonitorUI: {(uiExists ? "✅" : "❌")}");
            
            // Check scene management components
            bool sceneManagerExists = FindFirstObjectByType<SceneManagerBTR>() != null;
            bool sceneEventsManagerExists = SceneEventsManager.Instance != null;
            bool gameEventsManagerExists = GameEventsManager.Instance != null;
            bool loadingScreenExists = LoadingScreen.Instance != null;
            
            Debug.Log($"SceneManagerBTR: {(sceneManagerExists ? "✅" : "❌")}");
            Debug.Log($"SceneEventsManager: {(sceneEventsManagerExists ? "✅" : "❌")}");
            Debug.Log($"GameEventsManager: {(gameEventsManagerExists ? "✅" : "❌")}");
            Debug.Log($"LoadingScreen: {(loadingScreenExists ? "✅" : "❌")}");
            
            bool allSystemsReady = diagnosticExists && trackerExists && recoveryExists && 
                                   sceneManagerExists && sceneEventsManagerExists && gameEventsManagerExists;
            
            if (allSystemsReady)
            {
                Debug.Log("🟢 All systems ready for scene transition monitoring!");
            }
            else
            {
                Debug.LogWarning("🟡 Some components missing - diagnostic system may not work fully");
            }
        }
        
        [ContextMenu("Remove All Diagnostic Components")]
        public void RemoveAllDiagnosticComponents()
        {
            Debug.LogWarning("[SceneTransitionDiagnosticSetup] Removing all diagnostic components...");
            
            if (SceneTransitionDiagnostic.Instance != null)
            {
                DestroyImmediate(SceneTransitionDiagnostic.Instance.gameObject);
            }
            
            if (TransitionStateTracker.Instance != null)
            {
                DestroyImmediate(TransitionStateTracker.Instance.gameObject);
            }
            
            if (SceneTransitionRecovery.Instance != null)
            {
                DestroyImmediate(SceneTransitionRecovery.Instance.gameObject);
            }
            
            var monitorUI = FindFirstObjectByType<SceneTransitionMonitorUI>();
            if (monitorUI != null)
            {
                DestroyImmediate(monitorUI.gameObject);
            }
            
            hasBeenSetup = false;
            Debug.Log("[SceneTransitionDiagnosticSetup] All diagnostic components removed");
        }
        
        [ContextMenu("Test Scene Transition")]
        public void TestSceneTransition()
        {
            var sceneManager = FindFirstObjectByType<SceneManagerBTR>();
            if (sceneManager != null)
            {
                Debug.Log("[SceneTransitionDiagnosticSetup] Testing scene transition...");
                sceneManager.DebugChangeToNextScene();
            }
            else
            {
                Debug.LogError("[SceneTransitionDiagnosticSetup] SceneManagerBTR not found for testing");
            }
        }
        
        private void OnGUI()
        {
            if (!Application.isEditor) return;
            
            // Simple setup button in editor
            GUILayout.BeginArea(new Rect(10, 10, 200, 100));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("Scene Transition Diagnostics");
            
            if (GUILayout.Button("Setup System"))
            {
                SetupDiagnosticSystem();
            }
            
            if (GUILayout.Button("Validate System"))
            {
                ValidateSystem();
            }
            
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
    }
}
