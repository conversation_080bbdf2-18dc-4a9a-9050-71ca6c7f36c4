using UnityEngine;
using UnityEngine.UI;
using Stylo.MenUI;
using Debug = UnityEngine.Debug;

/// <summary>
/// Debug script to verify MenUI Settings Menu setup
/// Add this to any GameObject in your scene and check the console for setup verification
/// </summary>
public class MenUISettingsDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    public bool runOnStart = true;
    public bool enableDetailedLogging = true;

    private void Start()
    {
        if (runOnStart)
        {
            VerifyMenUISetup();
        }
    }

    [ContextMenu("Verify MenUI Setup")]
    public void VerifyMenUISetup()
    {
        Debug.Log("=== MenUI Settings Menu Setup Verification ===");

        // Find SimpleMenUISystem
        var menUISystem = FindObjectOfType<SimpleMenUISystem>();
        if (menUISystem == null)
        {
            Debug.LogError("❌ SimpleMenUISystem not found in scene!");
            return;
        }
        Debug.Log("✅ SimpleMenUISystem found");

        // Check lastUICanvas reference
        var lastUICanvasField = typeof(SimpleMenUISystem).GetField("lastUICanvas",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (lastUICanvasField != null)
        {
            var lastUICanvas = lastUICanvasField.GetValue(menUISystem) as GameObject;
            if (lastUICanvas == null)
            {
                Debug.LogError("❌ SimpleMenUISystem.lastUICanvas is not assigned!");
                return;
            }
            Debug.Log($"✅ lastUICanvas assigned: {lastUICanvas.name}");

            // Check if it's the Settings Menu Canvas
            if (lastUICanvas.name.Contains("Settings"))
            {
                Debug.Log("✅ lastUICanvas appears to be Settings Menu Canvas");
                VerifySettingsMenuCanvas(lastUICanvas);
            }
            else
            {
                Debug.LogWarning($"⚠️ lastUICanvas name '{lastUICanvas.name}' doesn't contain 'Settings' - verify this is correct");
            }
        }
        else
        {
            Debug.LogError("❌ Could not access lastUICanvas field via reflection");
        }

        Debug.Log("=== Verification Complete ===");
    }

    private void VerifySettingsMenuCanvas(GameObject settingsCanvas)
    {
        Debug.Log($"--- Verifying Settings Menu Canvas: {settingsCanvas.name} ---");

        // Check if canvas is initially inactive
        if (settingsCanvas.activeInHierarchy)
        {
            Debug.LogWarning("⚠️ Settings Menu Canvas is active in hierarchy - should be inactive by default");
        }
        else
        {
            Debug.Log("✅ Settings Menu Canvas is inactive by default");
        }

        // Check for StateManager component
        var stateManager = settingsCanvas.GetComponent<StateManager>();
        if (stateManager == null)
        {
            Debug.LogError("❌ StateManager component not found on Settings Menu Canvas");
            return;
        }
        Debug.Log("✅ StateManager component found");

        if (!stateManager.enabled)
        {
            Debug.LogWarning("⚠️ StateManager component is disabled");
        }
        else
        {
            Debug.Log("✅ StateManager component is enabled");
        }

        // Check FirstCanvas reference
        if (stateManager.FirstCanvas == null)
        {
            Debug.LogError("❌ StateManager.FirstCanvas is not assigned");
        }
        else
        {
            Debug.Log($"✅ StateManager.FirstCanvas assigned: {stateManager.FirstCanvas.name}");
        }

        // Check for state_menu GameObject
        var stateMenu = settingsCanvas.transform.Find("state_menu");
        if (stateMenu == null)
        {
            Debug.LogError("❌ state_menu GameObject not found as child of Settings Menu Canvas");

            // List all children for debugging
            Debug.Log("Available children:");
            for (int i = 0; i < settingsCanvas.transform.childCount; i++)
            {
                var child = settingsCanvas.transform.GetChild(i);
                Debug.Log($"  - {child.name} (active: {child.gameObject.activeSelf})");
            }
        }
        else
        {
            Debug.Log($"✅ state_menu found (active: {stateMenu.gameObject.activeSelf})");

            // Check for buttons in state_menu
            var buttons = stateMenu.GetComponentsInChildren<Button>(true);
            Debug.Log($"✅ Found {buttons.Length} buttons in state_menu");

            if (enableDetailedLogging)
            {
                foreach (var button in buttons)
                {
                    Debug.Log($"  - Button: {button.name} (interactable: {button.interactable}, navigation: {button.navigation.mode})");
                }
            }
        }

        // Check for LastUICanvasBridge
        var bridge = settingsCanvas.GetComponent<LastUICanvasBridge>();
        if (bridge == null)
        {
            Debug.LogWarning("⚠️ LastUICanvasBridge component not found - exit functionality may not work");
        }
        else
        {
            Debug.Log("✅ LastUICanvasBridge component found");
        }
    }

    [ContextMenu("Test Settings Menu Activation")]
    public void TestSettingsMenuActivation()
    {
        var menUISystem = FindObjectOfType<SimpleMenUISystem>();
        if (menUISystem == null)
        {
            Debug.LogError("SimpleMenUISystem not found!");
            return;
        }

        Debug.Log("Testing settings menu activation...");

        // Use reflection to call ShowLastUISettings
        var method = typeof(SimpleMenUISystem).GetMethod("ShowLastUISettings",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (method != null)
        {
            method.Invoke(menUISystem, null);
            Debug.Log("ShowLastUISettings called - check for any errors in console");
        }
        else
        {
            Debug.LogError("Could not find ShowLastUISettings method");
        }
    }
}
