using UnityEngine;
using System.IO;
using System.Text.RegularExpressions;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif

// First, provide the missing BTR.Debug methods to stop compilation errors
namespace BTR
{
    /// <summary>
    /// Temporary compatibility layer for BTR.Debug calls.
    /// This allows compilation while the migration tool fixes the references.
    /// </summary>
    public static class Debug
    {
        public static void Log(string message)
        {
            UnityEngine.Debug.Log(message);
        }

        public static void Log(string message, UnityEngine.Object context)
        {
            UnityEngine.Debug.Log(message, context);
        }

        public static void LogError(string message)
        {
            UnityEngine.Debug.LogError(message);
        }

        public static void LogError(string message, UnityEngine.Object context)
        {
            UnityEngine.Debug.LogError(message, context);
        }

        public static void LogWarning(string message)
        {
            UnityEngine.Debug.LogWarning(message);
        }

        public static void LogWarning(string message, UnityEngine.Object context)
        {
            UnityEngine.Debug.LogWarning(message, context);
        }

        public static void LogException(System.Exception exception)
        {
            UnityEngine.Debug.LogException(exception);
        }

        public static void LogException(System.Exception exception, UnityEngine.Object context)
        {
            UnityEngine.Debug.LogException(exception, context);
        }
    }
}

#if UNITY_EDITOR
namespace BTR.DebugMigration
{
    /// <summary>
    /// Tool to complete the BTR.Debug migration by automatically fixing remaining references
    /// </summary>
    public class BTRDebugMigrationTool : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<string> foundFiles = new List<string>();
        private bool scanComplete = false;

        [MenuItem("BTR/Debug Migration Tool")]
        public static void ShowWindow()
        {
            GetWindow<BTRDebugMigrationTool>("BTR Debug Migration");
        }

        private void OnGUI()
        {
            GUILayout.Label("BTR.Debug Migration Tool", EditorStyles.boldLabel);
            GUILayout.Space(10);

            GUILayout.Label("This tool will find and fix remaining BTR.Debug references in your project.");
            GUILayout.Label("According to your debug migration documentation, all BTR.Debug calls");
            GUILayout.Label("should have been converted to UnityEngine.Debug calls.");

            GUILayout.Space(10);

            if (GUILayout.Button("Scan for BTR.Debug References"))
            {
                ScanForBTRDebugReferences();
            }

            if (scanComplete)
            {
                GUILayout.Space(10);
                GUILayout.Label($"Found {foundFiles.Count} files with BTR.Debug references:", EditorStyles.boldLabel);

                scrollPosition = GUILayout.BeginScrollView(scrollPosition, GUILayout.Height(200));
                foreach (string file in foundFiles)
                {
                    GUILayout.Label(file);
                }
                GUILayout.EndScrollView();

                GUILayout.Space(10);

                if (foundFiles.Count > 0)
                {
                    if (GUILayout.Button("Fix All BTR.Debug References"))
                    {
                        FixAllBTRDebugReferences();
                    }
                }
                else
                {
                    GUILayout.Label("✅ No BTR.Debug references found!", EditorStyles.boldLabel);
                }
            }
        }

        private void ScanForBTRDebugReferences()
        {
            foundFiles.Clear();

            string[] csFiles = Directory.GetFiles(Application.dataPath, "*.cs", SearchOption.AllDirectories);

            foreach (string file in csFiles)
            {
                // Skip packages and third-party assets
                if (file.Contains("\\Packages\\") || file.Contains("/Packages/") ||
                    file.Contains("\\Feel\\") || file.Contains("/Feel/") ||
                    file.Contains("\\Reach\\") || file.Contains("/Reach/"))
                    continue;

                string content = File.ReadAllText(file);

                // Look for BTR.Debug references
                if (Regex.IsMatch(content, @"BTR\.Debug\.(Log|LogError|LogWarning)"))
                {
                    string relativePath = file.Replace(Application.dataPath, "Assets");
                    foundFiles.Add(relativePath);
                }
            }

            scanComplete = true;
            UnityEngine.Debug.Log($"[BTRDebugMigrationTool] Scan complete. Found {foundFiles.Count} files with BTR.Debug references.");
        }

        private void FixAllBTRDebugReferences()
        {
            int fixedFiles = 0;

            foreach (string relativePath in foundFiles)
            {
                string fullPath = relativePath.Replace("Assets", Application.dataPath);

                if (FixBTRDebugInFile(fullPath))
                {
                    fixedFiles++;
                }
            }

            AssetDatabase.Refresh();
            UnityEngine.Debug.Log($"[BTRDebugMigrationTool] Fixed BTR.Debug references in {fixedFiles} files.");

            // Re-scan to verify
            ScanForBTRDebugReferences();
        }

        private bool FixBTRDebugInFile(string filePath)
        {
            try
            {
                string content = File.ReadAllText(filePath);
                string originalContent = content;

                // Replace BTR.Debug.Log with Debug.Log
                content = Regex.Replace(content, @"BTR\.Debug\.Log\(", "Debug.Log(");

                // Replace BTR.Debug.LogError with Debug.LogError
                content = Regex.Replace(content, @"BTR\.Debug\.LogError\(", "Debug.LogError(");

                // Replace BTR.Debug.LogWarning with Debug.LogWarning
                content = Regex.Replace(content, @"BTR\.Debug\.LogWarning\(", "Debug.LogWarning(");

                if (content != originalContent)
                {
                    File.WriteAllText(filePath, content);
                    UnityEngine.Debug.Log($"[BTRDebugMigrationTool] Fixed: {filePath}");
                    return true;
                }
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"[BTRDebugMigrationTool] Error fixing {filePath}: {ex.Message}");
            }

            return false;
        }
    }
}
#endif
