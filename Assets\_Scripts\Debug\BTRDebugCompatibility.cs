using UnityEngine;

namespace BTR
{
    /// <summary>
    /// Compatibility layer for legacy BTR.Debug calls that were removed during debug migration.
    /// This provides the missing static methods that existing code expects.
    /// All calls are forwarded to Unity's standard Debug system.
    /// </summary>
    public static class Debug
    {
        /// <summary>
        /// Log a message using Unity's Debug.Log
        /// </summary>
        /// <param name="message">Message to log</param>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [System.Diagnostics.Conditional("DEVELOPMENT_BUILD")]
        public static void Log(string message)
        {
            UnityEngine.Debug.Log(message);
        }
        
        /// <summary>
        /// Log a message with context using Unity's Debug.Log
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="context">Unity object context</param>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [System.Diagnostics.Conditional("DEVELOPMENT_BUILD")]
        public static void Log(string message, Object context)
        {
            UnityEngine.Debug.Log(message, context);
        }
        
        /// <summary>
        /// Log an error message using Unity's Debug.LogError
        /// </summary>
        /// <param name="message">Error message to log</param>
        public static void LogError(string message)
        {
            UnityEngine.Debug.LogError(message);
        }
        
        /// <summary>
        /// Log an error message with context using Unity's Debug.LogError
        /// </summary>
        /// <param name="message">Error message to log</param>
        /// <param name="context">Unity object context</param>
        public static void LogError(string message, Object context)
        {
            UnityEngine.Debug.LogError(message, context);
        }
        
        /// <summary>
        /// Log a warning message using Unity's Debug.LogWarning
        /// </summary>
        /// <param name="message">Warning message to log</param>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [System.Diagnostics.Conditional("DEVELOPMENT_BUILD")]
        public static void LogWarning(string message)
        {
            UnityEngine.Debug.LogWarning(message);
        }
        
        /// <summary>
        /// Log a warning message with context using Unity's Debug.LogWarning
        /// </summary>
        /// <param name="message">Warning message to log</param>
        /// <param name="context">Unity object context</param>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [System.Diagnostics.Conditional("DEVELOPMENT_BUILD")]
        public static void LogWarning(string message, Object context)
        {
            UnityEngine.Debug.LogWarning(message, context);
        }
        
        /// <summary>
        /// Log an exception using Unity's Debug.LogException
        /// </summary>
        /// <param name="exception">Exception to log</param>
        public static void LogException(System.Exception exception)
        {
            UnityEngine.Debug.LogException(exception);
        }
        
        /// <summary>
        /// Log an exception with context using Unity's Debug.LogException
        /// </summary>
        /// <param name="exception">Exception to log</param>
        /// <param name="context">Unity object context</param>
        public static void LogException(System.Exception exception, Object context)
        {
            UnityEngine.Debug.LogException(exception, context);
        }
        
        /// <summary>
        /// Assert a condition using Unity's Debug.Assert
        /// </summary>
        /// <param name="condition">Condition to assert</param>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [System.Diagnostics.Conditional("DEVELOPMENT_BUILD")]
        public static void Assert(bool condition)
        {
            UnityEngine.Debug.Assert(condition);
        }
        
        /// <summary>
        /// Assert a condition with message using Unity's Debug.Assert
        /// </summary>
        /// <param name="condition">Condition to assert</param>
        /// <param name="message">Message to show if assertion fails</param>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [System.Diagnostics.Conditional("DEVELOPMENT_BUILD")]
        public static void Assert(bool condition, string message)
        {
            UnityEngine.Debug.Assert(condition, message);
        }
        
        /// <summary>
        /// Assert a condition with message and context using Unity's Debug.Assert
        /// </summary>
        /// <param name="condition">Condition to assert</param>
        /// <param name="message">Message to show if assertion fails</param>
        /// <param name="context">Unity object context</param>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [System.Diagnostics.Conditional("DEVELOPMENT_BUILD")]
        public static void Assert(bool condition, string message, Object context)
        {
            UnityEngine.Debug.Assert(condition, message, context);
        }
    }
}
