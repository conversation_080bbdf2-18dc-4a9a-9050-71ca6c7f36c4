# BTR.Debug Migration Instructions

## Quick Fix for Compilation Errors

You're seeing hundreds of compilation errors like:
```
error CS0104: 'Debug' is an ambiguous reference between 'BTR.Debug' and 'UnityEngine.Debug'
```

This is because your project's debug migration from `BTR.Debug` to Unity's `Debug` system was incomplete.

## Solution: Use the Migration Tool

### Step 1: Open the Migration Tool
1. In Unity Editor, go to the top menu
2. Click **BTR > Debug Migration Tool**
3. A window will open titled "BTR Debug Migration"

### Step 2: Scan for Problems
1. Click **"Scan for BTR.Debug References"**
2. The tool will search all your C# files
3. It will show you a list of files that still have `BTR.Debug` references

### Step 3: Fix All Problems
1. Click **"Fix All BTR.Debug References"**
2. The tool will automatically convert:
   - `BTR.Debug.Log(...)` → `Debug.Log(...)`
   - `BTR.Debug.LogError(...)` → `Debug.LogError(...)`
   - `BTR.Debug.LogWarning(...)` → `Debug.LogWarning(...)`
3. Unity will refresh and recompile
4. All compilation errors should be resolved

## What This Does

The migration tool completes the debug system migration that was started earlier:

**Before (causing errors):**
```csharp
BTR.Debug.Log("Message");
BTR.Debug.LogError("Error");
```

**After (working correctly):**
```csharp
Debug.Log("Message");
Debug.LogError("Error");
```

## Why This Happened

1. Your project migrated from a custom `BTR.Debug` system to Unity's standard `Debug` system
2. The migration documentation indicated it was complete
3. However, some files weren't properly converted during the migration
4. When I created the scene transition diagnostic system, it exposed these remaining references
5. The solution is to complete the migration, not create a compatibility layer

## Verification

After running the migration tool:
1. All compilation errors should be gone
2. Your existing debug logging will work exactly the same
3. The scene transition diagnostic system will work properly
4. No performance impact or behavior changes

## If You Still Have Issues

If you still see compilation errors after running the migration tool:

1. **Check the Unity Console** for any specific error messages
2. **Run the tool again** - click "Scan" to see if any files were missed
3. **Manual fix** - For any remaining files, manually replace:
   - `BTR.Debug.Log` with `Debug.Log`
   - `BTR.Debug.LogError` with `Debug.LogError`
   - `BTR.Debug.LogWarning` with `Debug.LogWarning`

## Scene Transition Diagnostics

Once the migration is complete, you can use the scene transition diagnostic system:

1. Add `SceneTransitionDiagnosticSetup` to your base scene
2. Enable "Auto Setup On Awake"
3. Press **F12** during gameplay to see the diagnostic UI
4. Monitor scene transitions and get automatic recovery when they get stuck

The diagnostic system uses the namespace `BTR.SceneTransition.Debug` to avoid any conflicts with your main debug system.
