using UnityEngine;
using UnityEngine.SceneManagement;
using System;
using System.Collections;
using System.Threading;
using Cysharp.Threading.Tasks;
using BTR;

namespace BTR.Debug
{
    /// <summary>
    /// Recovery and intervention tools for stuck scene transitions
    /// </summary>
    public class SceneTransitionRecovery : MonoBehaviour
    {
        [Header("Recovery Settings")]
        [SerializeField] private bool enableAutoRecovery = true;
        [SerializeField] private float autoRecoveryDelay = 5f;
        [SerializeField] private int maxAutoRecoveryAttempts = 3;
        
        [Header("Emergency Settings")]
        [SerializeField] private string fallbackSceneName = "Ouroboros - Base";
        [SerializeField] private bool enableEmergencyReset = true;
        
        // Recovery State
        private int autoRecoveryAttempts = 0;
        private bool isRecovering = false;
        private CancellationTokenSource recoveryToken;
        
        // Component References
        private SceneTransitionDiagnostic diagnostic;
        private SceneManagerBTR sceneManager;
        private LoadingScreen loadingScreen;
        
        public static SceneTransitionRecovery Instance { get; private set; }
        
        // Events
        public event Action<RecoveryMethod, bool> OnRecoveryAttempted;
        public event Action<string> OnEmergencyReset;
        
        public enum RecoveryMethod
        {
            ForceSceneLoad,
            RestartLoadingScreen,
            ClearAsyncOperations,
            ResetSceneManager,
            EmergencySceneLoad,
            FullSystemReset
        }
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeRecovery();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeRecovery()
        {
            // Find diagnostic component
            diagnostic = SceneTransitionDiagnostic.Instance;
            if (diagnostic != null)
            {
                diagnostic.OnStateChanged += OnTransitionStateChanged;
                diagnostic.OnIssueDetected += OnIssueDetected;
            }
            
            // Find other components
            StartCoroutine(FindComponentsAsync());
        }
        
        private IEnumerator FindComponentsAsync()
        {
            yield return null; // Wait a frame
            
            sceneManager = FindFirstObjectByType<SceneManagerBTR>();
            loadingScreen = LoadingScreen.Instance;
            
            Debug.Log($"[SceneTransitionRecovery] Components found - SceneManager: {sceneManager != null}, LoadingScreen: {loadingScreen != null}");
        }
        
        private void OnTransitionStateChanged(SceneTransitionDiagnostic.TransitionState newState)
        {
            if (newState == SceneTransitionDiagnostic.TransitionState.Stuck && enableAutoRecovery && !isRecovering)
            {
                _ = StartAutoRecovery();
            }
            else if (newState == SceneTransitionDiagnostic.TransitionState.LoadCompleted || 
                     newState == SceneTransitionDiagnostic.TransitionState.Idle)
            {
                // Reset recovery attempts on successful completion
                autoRecoveryAttempts = 0;
            }
        }
        
        private void OnIssueDetected(string issue)
        {
            Debug.LogWarning($"[SceneTransitionRecovery] Issue detected: {issue}");
            
            if (enableAutoRecovery && !isRecovering && autoRecoveryAttempts < maxAutoRecoveryAttempts)
            {
                _ = StartAutoRecovery();
            }
        }
        
        private async UniTask StartAutoRecovery()
        {
            if (isRecovering) return;
            
            isRecovering = true;
            autoRecoveryAttempts++;
            
            Debug.LogWarning($"[SceneTransitionRecovery] Starting auto recovery attempt #{autoRecoveryAttempts}");
            
            try
            {
                recoveryToken?.Cancel();
                recoveryToken = new CancellationTokenSource();
                
                // Wait before attempting recovery
                await UniTask.Delay(TimeSpan.FromSeconds(autoRecoveryDelay), cancellationToken: recoveryToken.Token);
                
                // Try recovery methods in order of severity
                bool recovered = false;
                
                if (!recovered && autoRecoveryAttempts == 1)
                {
                    recovered = await TryRecoveryMethod(RecoveryMethod.ForceSceneLoad);
                }
                
                if (!recovered && autoRecoveryAttempts == 2)
                {
                    recovered = await TryRecoveryMethod(RecoveryMethod.RestartLoadingScreen);
                }
                
                if (!recovered && autoRecoveryAttempts >= 3)
                {
                    recovered = await TryRecoveryMethod(RecoveryMethod.EmergencySceneLoad);
                }
                
                if (recovered)
                {
                    Debug.Log($"[SceneTransitionRecovery] Auto recovery successful on attempt #{autoRecoveryAttempts}");
                    autoRecoveryAttempts = 0;
                }
                else
                {
                    Debug.LogError($"[SceneTransitionRecovery] Auto recovery failed on attempt #{autoRecoveryAttempts}");
                    
                    if (autoRecoveryAttempts >= maxAutoRecoveryAttempts)
                    {
                        Debug.LogError("[SceneTransitionRecovery] Maximum auto recovery attempts reached - manual intervention required");
                    }
                }
            }
            catch (OperationCanceledException)
            {
                Debug.Log("[SceneTransitionRecovery] Auto recovery cancelled");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[SceneTransitionRecovery] Auto recovery failed with exception: {ex.Message}");
            }
            finally
            {
                isRecovering = false;
            }
        }
        
        public async UniTask<bool> TryRecoveryMethod(RecoveryMethod method)
        {
            Debug.Log($"[SceneTransitionRecovery] Attempting recovery method: {method}");
            
            try
            {
                bool success = false;
                
                switch (method)
                {
                    case RecoveryMethod.ForceSceneLoad:
                        success = await ForceSceneLoad();
                        break;
                        
                    case RecoveryMethod.RestartLoadingScreen:
                        success = await RestartLoadingScreen();
                        break;
                        
                    case RecoveryMethod.ClearAsyncOperations:
                        success = await ClearAsyncOperations();
                        break;
                        
                    case RecoveryMethod.ResetSceneManager:
                        success = await ResetSceneManager();
                        break;
                        
                    case RecoveryMethod.EmergencySceneLoad:
                        success = await EmergencySceneLoad();
                        break;
                        
                    case RecoveryMethod.FullSystemReset:
                        success = await FullSystemReset();
                        break;
                }
                
                OnRecoveryAttempted?.Invoke(method, success);
                return success;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[SceneTransitionRecovery] Recovery method {method} failed: {ex.Message}");
                OnRecoveryAttempted?.Invoke(method, false);
                return false;
            }
        }
        
        private async UniTask<bool> ForceSceneLoad()
        {
            if (sceneManager == null || diagnostic == null) return false;
            
            string sceneName = diagnostic.CurrentSceneName;
            if (string.IsNullOrEmpty(sceneName)) return false;
            
            Debug.Log($"[SceneTransitionRecovery] Force loading scene: {sceneName}");
            sceneManager.LoadScene(sceneName);
            
            // Wait to see if it works
            await UniTask.Delay(3000);
            return diagnostic.CurrentState != SceneTransitionDiagnostic.TransitionState.Stuck;
        }
        
        private async UniTask<bool> RestartLoadingScreen()
        {
            if (loadingScreen == null) return false;
            
            Debug.Log("[SceneTransitionRecovery] Restarting loading screen");
            
            try
            {
                // Try to restart the loading screen fade
                await loadingScreen.StartFadeOut();
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[SceneTransitionRecovery] Failed to restart loading screen: {ex.Message}");
                return false;
            }
        }
        
        private async UniTask<bool> ClearAsyncOperations()
        {
            Debug.Log("[SceneTransitionRecovery] Clearing async operations");
            
            // Force garbage collection to clean up any stuck operations
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            await UniTask.Delay(1000);
            return true;
        }
        
        private async UniTask<bool> ResetSceneManager()
        {
            if (sceneManager == null) return false;
            
            Debug.Log("[SceneTransitionRecovery] Resetting scene manager state");
            
            // Try to reset the scene manager to a clean state
            if (diagnostic != null)
            {
                diagnostic.ForceResetTransitionState();
            }
            
            await UniTask.Delay(1000);
            return true;
        }
        
        private async UniTask<bool> EmergencySceneLoad()
        {
            Debug.LogWarning($"[SceneTransitionRecovery] Emergency scene load to: {fallbackSceneName}");
            
            try
            {
                // Force load the fallback scene
                var asyncLoad = SceneManager.LoadSceneAsync(fallbackSceneName, LoadSceneMode.Single);
                
                while (!asyncLoad.isDone)
                {
                    await UniTask.Yield();
                }
                
                OnEmergencyReset?.Invoke($"Emergency loaded scene: {fallbackSceneName}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[SceneTransitionRecovery] Emergency scene load failed: {ex.Message}");
                return false;
            }
        }
        
        private async UniTask<bool> FullSystemReset()
        {
            Debug.LogError("[SceneTransitionRecovery] Performing full system reset");
            
            try
            {
                // Reset all systems
                if (diagnostic != null)
                {
                    diagnostic.ForceResetTransitionState();
                }
                
                // Clear all locks
                if (GameManager.Instance != null)
                {
                    GameManager.Instance.ClearAllPlayerLocks();
                }
                
                // Force garbage collection
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                // Load fallback scene
                await EmergencySceneLoad();
                
                OnEmergencyReset?.Invoke("Full system reset performed");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[SceneTransitionRecovery] Full system reset failed: {ex.Message}");
                return false;
            }
        }
        
        // Manual intervention methods
        [ContextMenu("Manual Force Scene Load")]
        public void ManualForceSceneLoad()
        {
            _ = TryRecoveryMethod(RecoveryMethod.ForceSceneLoad);
        }
        
        [ContextMenu("Manual Emergency Reset")]
        public void ManualEmergencyReset()
        {
            _ = TryRecoveryMethod(RecoveryMethod.EmergencySceneLoad);
        }
        
        [ContextMenu("Manual Full System Reset")]
        public void ManualFullSystemReset()
        {
            _ = TryRecoveryMethod(RecoveryMethod.FullSystemReset);
        }
        
        public void SetFallbackScene(string sceneName)
        {
            fallbackSceneName = sceneName;
            Debug.Log($"[SceneTransitionRecovery] Fallback scene set to: {sceneName}");
        }
        
        private void OnDestroy()
        {
            recoveryToken?.Cancel();
            recoveryToken?.Dispose();
            
            if (diagnostic != null)
            {
                diagnostic.OnStateChanged -= OnTransitionStateChanged;
                diagnostic.OnIssueDetected -= OnIssueDetected;
            }
        }
    }
}
