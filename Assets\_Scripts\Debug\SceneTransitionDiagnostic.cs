using UnityEngine;
using UnityEngine.SceneManagement;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using Cysharp.Threading.Tasks;
using BTR;

namespace BTR.SceneTransition.Debug
{
    /// <summary>
    /// Comprehensive diagnostic tool for monitoring scene transitions and detecting issues
    /// </summary>
    [DefaultExecutionOrder(-100)] // Initialize early to catch all events
    public class SceneTransitionDiagnostic : MonoBehaviour
    {
        [Header("Diagnostic Settings")]
        [SerializeField] private bool enableDiagnostics = true;
        [SerializeField] private bool enableDetailedLogging = true;
        [SerializeField] private bool enablePerformanceMonitoring = true;
        [SerializeField] private bool enableUIDisplay = true;
        [SerializeField] private float stuckDetectionTimeout = 30f; // seconds

        [Header("Auto Recovery")]
        [SerializeField] private bool enableAutoRecovery = true;
        [SerializeField] private int maxRecoveryAttempts = 3;

        // Diagnostic State
        private TransitionState currentState = TransitionState.Idle;
        private string currentSceneName = "";
        private float transitionStartTime;
        private float lastProgressUpdate;
        private float currentProgress;
        private List<string> eventLog = new List<string>();
        private Dictionary<string, float> performanceMetrics = new Dictionary<string, float>();

        // Component References
        private SceneManagerBTR sceneManager;
        private SceneEvents sceneEvents;
        private GameEvents gameEvents;
        private LoadingScreen loadingScreen;

        // Recovery State
        private int recoveryAttempts = 0;
        private CancellationTokenSource recoveryToken;

        // Monitoring
        private Stopwatch transitionTimer = new Stopwatch();
        private bool isMonitoring = false;

        public enum TransitionState
        {
            Idle,
            LoadRequested,
            LoadStarted,
            LoadInProgress,
            LoadCompleted,
            FadeInStarted,
            FadeInCompleted,
            FadeOutStarted,
            FadeOutCompleted,
            Stuck,
            Error,
            Recovering
        }

        public static SceneTransitionDiagnostic Instance { get; private set; }

        // Events for external monitoring
        public event Action<TransitionState> OnStateChanged;
        public event Action<string> OnIssueDetected;
        public event Action<string> OnRecoveryAttempted;

        // Public properties for monitoring
        public TransitionState CurrentState => currentState;
        public string CurrentSceneName => currentSceneName;
        public float TransitionDuration => transitionTimer.IsRunning ? (float)transitionTimer.Elapsed.TotalSeconds : 0f;
        public float CurrentProgress => currentProgress;
        public bool IsStuck => currentState == TransitionState.Stuck;
        public List<string> EventLog => new List<string>(eventLog); // Return copy for safety

        public enum LogLevel
        {
            Info,
            Warning,
            Error
        }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeDiagnostics();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void InitializeDiagnostics()
        {
            if (!enableDiagnostics) return;

            LogEvent("SceneTransitionDiagnostic initialized");

            // Find required components
            StartCoroutine(FindComponentsAsync());

            // Subscribe to Unity scene events
            SceneManager.sceneLoaded += OnUnitySceneLoaded;
            SceneManager.sceneUnloaded += OnUnitySceneUnloaded;

            // Start monitoring coroutine
            StartCoroutine(MonitoringCoroutine());
        }

        private System.Collections.IEnumerator FindComponentsAsync()
        {
            // Wait a frame for other systems to initialize
            yield return null;

            // Find SceneManagerBTR
            sceneManager = FindFirstObjectByType<SceneManagerBTR>();
            if (sceneManager == null)
            {
                LogEvent("WARNING: SceneManagerBTR not found!", LogLevel.Warning);
            }
            else
            {
                LogEvent("SceneManagerBTR found and connected");
            }

            // Find LoadingScreen
            loadingScreen = LoadingScreen.Instance;
            if (loadingScreen == null)
            {
                LogEvent("WARNING: LoadingScreen not found!", LogLevel.Warning);
            }
            else
            {
                LogEvent("LoadingScreen found and connected");
            }

            // Get SceneEvents
            if (SceneEventsManager.Instance != null)
            {
                sceneEvents = SceneEventsManager.Instance.Events;
                if (sceneEvents != null)
                {
                    SubscribeToSceneEvents();
                    LogEvent("SceneEvents connected");
                }
                else
                {
                    LogEvent("WARNING: SceneEvents asset not found!", LogLevel.Warning);
                }
            }
            else
            {
                LogEvent("WARNING: SceneEventsManager not found!", LogLevel.Warning);
            }

            // Get GameEvents
            if (GameEventsManager.Instance != null)
            {
                gameEvents = GameEventsManager.Instance.Events;
                if (gameEvents != null)
                {
                    SubscribeToGameEvents();
                    LogEvent("GameEvents connected");
                }
                else
                {
                    LogEvent("WARNING: GameEvents asset not found!", LogLevel.Warning);
                }
            }
            else
            {
                LogEvent("WARNING: GameEventsManager not found!", LogLevel.Warning);
            }
        }

        private void SubscribeToSceneEvents()
        {
            if (sceneEvents == null) return;

            sceneEvents.OnSceneLoadRequested += OnSceneLoadRequested;
            sceneEvents.OnSceneLoadStarted += OnSceneLoadStarted;
            sceneEvents.OnSceneLoadCompleted += OnSceneLoadCompleted;
            sceneEvents.OnSceneUnloadStarted += OnSceneUnloadStarted;
            sceneEvents.OnSceneUnloadCompleted += OnSceneUnloadCompleted;
            sceneEvents.OnSceneActivated += OnSceneActivated;
            sceneEvents.OnSceneDeactivated += OnSceneDeactivated;
        }

        private void SubscribeToGameEvents()
        {
            if (gameEvents == null) return;

            gameEvents.OnSceneTransitionStarted += OnSceneTransitionStarted;
            gameEvents.OnSceneTransitionCompleted += OnSceneTransitionCompleted;
            gameEvents.OnSceneLoadProgress += OnSceneLoadProgress;
        }

        private void LogEvent(string message, LogLevel level = LogLevel.Info)
        {
            if (!enableDetailedLogging) return;

            string timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            string logMessage = $"[{timestamp}] [{level}] {message}";

            eventLog.Add(logMessage);

            // Keep log size manageable
            if (eventLog.Count > 100)
            {
                eventLog.RemoveAt(0);
            }

            // Output to Unity console based on level
            switch (level)
            {
                case LogLevel.Info:
                    UnityEngine.Debug.Log($"[SceneTransitionDiagnostic] {logMessage}");
                    break;
                case LogLevel.Warning:
                    UnityEngine.Debug.LogWarning($"[SceneTransitionDiagnostic] {logMessage}");
                    break;
                case LogLevel.Error:
                    UnityEngine.Debug.LogError($"[SceneTransitionDiagnostic] {logMessage}");
                    break;
            }
        }

        private void ChangeState(TransitionState newState)
        {
            if (currentState == newState) return;

            TransitionState oldState = currentState;
            currentState = newState;

            LogEvent($"State changed: {oldState} -> {newState}");
            OnStateChanged?.Invoke(newState);

            // Handle state-specific logic
            switch (newState)
            {
                case TransitionState.LoadRequested:
                    transitionTimer.Restart();
                    transitionStartTime = Time.realtimeSinceStartup;
                    lastProgressUpdate = Time.realtimeSinceStartup;
                    currentProgress = 0f;
                    recoveryAttempts = 0;
                    break;

                case TransitionState.LoadCompleted:
                case TransitionState.FadeOutCompleted:
                    if (transitionTimer.IsRunning)
                    {
                        transitionTimer.Stop();
                        float duration = (float)transitionTimer.Elapsed.TotalSeconds;
                        performanceMetrics[$"Transition_{currentSceneName}"] = duration;
                        LogEvent($"Transition completed in {duration:F2} seconds");
                    }
                    break;

                case TransitionState.Stuck:
                    OnIssueDetected?.Invoke($"Scene transition stuck in state {oldState} for {stuckDetectionTimeout} seconds");
                    if (enableAutoRecovery && recoveryAttempts < maxRecoveryAttempts)
                    {
                        _ = AttemptRecovery();
                    }
                    break;

                case TransitionState.Error:
                    OnIssueDetected?.Invoke($"Scene transition error detected");
                    break;
            }
        }

        private System.Collections.IEnumerator MonitoringCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(1f); // Check every second

                if (!enableDiagnostics || currentState == TransitionState.Idle)
                    continue;

                // Check for stuck transitions
                float timeSinceLastUpdate = Time.realtimeSinceStartup - lastProgressUpdate;
                if (timeSinceLastUpdate > stuckDetectionTimeout && currentState != TransitionState.Stuck)
                {
                    LogEvent($"Transition appears stuck - no progress for {timeSinceLastUpdate:F1} seconds", LogLevel.Warning);
                    ChangeState(TransitionState.Stuck);
                }

                // Monitor memory usage if performance monitoring is enabled
                if (enablePerformanceMonitoring)
                {
                    long memoryUsage = GC.GetTotalMemory(false);
                    performanceMetrics["CurrentMemoryMB"] = memoryUsage / (1024f * 1024f);
                }
            }
        }

        // Event Handlers for Scene Events
        private void OnSceneLoadRequested(string sceneName)
        {
            currentSceneName = sceneName;
            ChangeState(TransitionState.LoadRequested);
            LogEvent($"Scene load requested: {sceneName}");
        }

        private void OnSceneLoadStarted(string sceneName)
        {
            ChangeState(TransitionState.LoadStarted);
            LogEvent($"Scene load started: {sceneName}");
        }

        private void OnSceneLoadCompleted(string sceneName)
        {
            ChangeState(TransitionState.LoadCompleted);
            LogEvent($"Scene load completed: {sceneName}");
        }

        private void OnSceneUnloadStarted(string sceneName)
        {
            LogEvent($"Scene unload started: {sceneName}");
        }

        private void OnSceneUnloadCompleted(string sceneName)
        {
            LogEvent($"Scene unload completed: {sceneName}");
        }

        private void OnSceneActivated(string sceneName)
        {
            LogEvent($"Scene activated: {sceneName}");
        }

        private void OnSceneDeactivated(string sceneName)
        {
            LogEvent($"Scene deactivated: {sceneName}");
        }

        // Event Handlers for Game Events
        private void OnSceneTransitionStarted()
        {
            ChangeState(TransitionState.FadeInStarted);
            LogEvent("Scene transition started (fade in)");
        }

        private void OnSceneTransitionCompleted()
        {
            ChangeState(TransitionState.FadeOutCompleted);
            LogEvent("Scene transition completed (fade out)");
        }

        private void OnSceneLoadProgress(float progress)
        {
            currentProgress = progress;
            lastProgressUpdate = Time.realtimeSinceStartup;

            if (currentState == TransitionState.LoadStarted || currentState == TransitionState.LoadInProgress)
            {
                ChangeState(TransitionState.LoadInProgress);
            }

            LogEvent($"Scene load progress: {progress:P1}");
        }

        // Unity Scene Event Handlers
        private void OnUnitySceneLoaded(Scene scene, LoadSceneMode mode)
        {
            LogEvent($"Unity scene loaded: {scene.name} (mode: {mode})");
        }

        private void OnUnitySceneUnloaded(Scene scene)
        {
            LogEvent($"Unity scene unloaded: {scene.name}");
        }

        // Recovery Methods
        private async UniTask AttemptRecovery()
        {
            recoveryAttempts++;
            ChangeState(TransitionState.Recovering);

            string recoveryMessage = $"Attempting recovery #{recoveryAttempts} for stuck transition";
            LogEvent(recoveryMessage, LogLevel.Warning);
            OnRecoveryAttempted?.Invoke(recoveryMessage);

            try
            {
                recoveryToken?.Cancel();
                recoveryToken = new CancellationTokenSource();

                // Wait a moment for any pending operations to complete
                await UniTask.Delay(1000, cancellationToken: recoveryToken.Token);

                // Try different recovery strategies based on current state
                bool recovered = false;

                switch (currentState)
                {
                    case TransitionState.Recovering:
                        recovered = await TryForceSceneLoad();
                        break;
                }

                if (recovered)
                {
                    LogEvent($"Recovery attempt #{recoveryAttempts} successful", LogLevel.Info);
                    ChangeState(TransitionState.LoadCompleted);
                }
                else
                {
                    LogEvent($"Recovery attempt #{recoveryAttempts} failed", LogLevel.Error);
                    if (recoveryAttempts >= maxRecoveryAttempts)
                    {
                        ChangeState(TransitionState.Error);
                        LogEvent("Maximum recovery attempts reached - manual intervention required", LogLevel.Error);
                    }
                    else
                    {
                        ChangeState(TransitionState.Stuck);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogEvent("Recovery cancelled", LogLevel.Warning);
            }
            catch (Exception ex)
            {
                LogEvent($"Recovery failed with exception: {ex.Message}", LogLevel.Error);
                ChangeState(TransitionState.Error);
            }
        }

        private async UniTask<bool> TryForceSceneLoad()
        {
            try
            {
                if (sceneManager != null && !string.IsNullOrEmpty(currentSceneName))
                {
                    LogEvent($"Attempting forced scene load: {currentSceneName}");

                    // Try to trigger the scene load again
                    sceneManager.LoadScene(currentSceneName);

                    // Wait for a reasonable time to see if it works
                    await UniTask.Delay(5000, cancellationToken: recoveryToken.Token);

                    return true; // If we get here without exception, consider it successful
                }
            }
            catch (Exception ex)
            {
                LogEvent($"Forced scene load failed: {ex.Message}", LogLevel.Error);
            }

            return false;
        }

        // Public Methods for Manual Intervention
        [ContextMenu("Force Reset Transition State")]
        public void ForceResetTransitionState()
        {
            LogEvent("Manually resetting transition state", LogLevel.Warning);
            ChangeState(TransitionState.Idle);
            currentSceneName = "";
            currentProgress = 0f;
            transitionTimer.Reset();
            recoveryAttempts = 0;
        }

        [ContextMenu("Force Scene Load")]
        public void ForceSceneLoad()
        {
            if (sceneManager != null && !string.IsNullOrEmpty(currentSceneName))
            {
                LogEvent($"Manually forcing scene load: {currentSceneName}", LogLevel.Warning);
                sceneManager.LoadScene(currentSceneName);
            }
            else
            {
                LogEvent("Cannot force scene load - no scene name or SceneManagerBTR", LogLevel.Error);
            }
        }

        [ContextMenu("Clear Event Log")]
        public void ClearEventLog()
        {
            eventLog.Clear();
            LogEvent("Event log cleared");
        }

        // Cleanup
        private void OnDestroy()
        {
            recoveryToken?.Cancel();
            recoveryToken?.Dispose();

            // Unsubscribe from Unity events
            SceneManager.sceneLoaded -= OnUnitySceneLoaded;
            SceneManager.sceneUnloaded -= OnUnitySceneUnloaded;

            // Unsubscribe from custom events
            if (sceneEvents != null)
            {
                sceneEvents.OnSceneLoadRequested -= OnSceneLoadRequested;
                sceneEvents.OnSceneLoadStarted -= OnSceneLoadStarted;
                sceneEvents.OnSceneLoadCompleted -= OnSceneLoadCompleted;
                sceneEvents.OnSceneUnloadStarted -= OnSceneUnloadStarted;
                sceneEvents.OnSceneUnloadCompleted -= OnSceneUnloadCompleted;
                sceneEvents.OnSceneActivated -= OnSceneActivated;
                sceneEvents.OnSceneDeactivated -= OnSceneDeactivated;
            }

            if (gameEvents != null)
            {
                gameEvents.OnSceneTransitionStarted -= OnSceneTransitionStarted;
                gameEvents.OnSceneTransitionCompleted -= OnSceneTransitionCompleted;
                gameEvents.OnSceneLoadProgress -= OnSceneLoadProgress;
            }
        }
    }
}
